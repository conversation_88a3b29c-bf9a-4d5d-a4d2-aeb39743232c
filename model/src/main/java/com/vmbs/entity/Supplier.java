package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商表
 * @TableName supplier
 */
@TableName(value ="supplier")
@Data
@EqualsAndHashCode(callSuper = false)
public class Supplier extends BaseEntity {
    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplier_name;

    /**
     * 联系人
     */
    @TableField(value = "contact_person")
    private String contact_person;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;
}