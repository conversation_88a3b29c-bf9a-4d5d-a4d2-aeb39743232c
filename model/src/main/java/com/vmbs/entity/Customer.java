package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.vmbs.enums.CustomerStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户表
 * @TableName customer
 */
@TableName(value ="customer")
@Data
@EqualsAndHashCode(callSuper = false)
public class Customer extends BaseEntity {
    /**
     * 姓名
     */
    @TableField(value = "customer_name")
    private String customer_name;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 客户头像
     */
    @TableField(value = "customer_avatar")
    private String customer_avatar;

    /**
     * 个人储值余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 个人会员积分
     */
    @TableField(value = "points")
    private Integer points;

    /**
     * 客户状态
     */
    @TableField(value = "status")
    private CustomerStatusEnum status;
}