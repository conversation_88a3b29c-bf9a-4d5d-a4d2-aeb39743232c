package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 角色权限关联表
 * @TableName role_permission
 */
@TableName(value ="role_permission")
@Data
public class RolePermission extends BaseEntity {
    /**
     * 角色ID
     */
    @TableField(value = "role_id")
    private Integer role_id;

    /**
     * 权限ID
     */
    @TableField(value = "permission_id")
    private Integer permission_id;
}