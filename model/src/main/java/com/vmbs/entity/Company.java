package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 公司表
 * @TableName company
 */
@TableName(value ="company")
@Data
public class Company extends BaseEntity {
    /**
     * 公司名称
     */
    @TableField(value = "company_name")
    private String company_name;

    /**
     * 税号
     */
    @TableField(value = "tax_id")
    private String tax_id;

    /**
     * 公司地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 联系人
     */
    @TableField(value = "contact_person")
    private String contact_person;
}