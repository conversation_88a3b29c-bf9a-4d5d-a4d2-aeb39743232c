package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单-优惠券关联表
 * @TableName order_coupon
 */
@TableName(value ="order_coupon")
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderCoupon extends BaseEntity {
    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private Long order_id;

    /**
     * 使用的优惠券ID
     */
    @TableField(value = "coupon_id")
    private Long coupon_id;
}