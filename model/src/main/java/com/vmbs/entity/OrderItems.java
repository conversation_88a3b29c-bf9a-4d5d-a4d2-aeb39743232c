package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单详情表
 * @TableName order_items
 */
@TableName(value ="order_items")
@Data
public class OrderItems extends BaseEntity {
    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private Long order_id;

    /**
     * 商品/服务ID
     */
    @TableField(value = "goods_id")
    private Long goods_id;

    /**
     * 项目类型
     */
    @TableField(value = "item_type")
    private Object item_type;

    /**
     * 数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 下单时单价
     */
    @TableField(value = "price")
    private BigDecimal price;
}