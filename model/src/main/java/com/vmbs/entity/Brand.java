package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 品牌表
 * @TableName brand
 */
@TableName(value ="brand")
@Data
public class Brand extends BaseEntity {
    /**
     * 品牌名称
     */
    @TableField(value = "brand_name")
    private String brand_name;
}