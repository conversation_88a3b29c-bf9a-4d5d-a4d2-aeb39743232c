package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.vmbs.enums.GoodsStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品表
 * @TableName goods
 */
@TableName(value ="goods")
@Data
@EqualsAndHashCode(callSuper = false)
public class Goods extends BaseEntity {
    /**
     * 商品名称
     */
    @TableField(value = "goods_name")
    private String goods_name;

    /**
     * 商品编码(SKU)
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * 商品图片
     */
    @TableField(value = "goods_pic")
    private String goods_pic;

    /**
     * 商品描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 当前库存数量
     */
    @TableField(value = "stock")
    private Integer stock;

    /**
     * 单价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 成本价
     */
    @TableField(value = "cost_price")
    private BigDecimal cost_price;

    /**
     * 分类ID
     */
    @TableField(value = "category_id")
    private Long category_id;

    /**
     * 品牌ID
     */
    @TableField(value = "brand_id")
    private Long brand_id;

    /**
     * 状态
     */
    @TableField(value = "status")
    private GoodsStatusEnum status;
}