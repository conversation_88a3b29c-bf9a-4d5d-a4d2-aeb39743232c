package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.vmbs.enums.OrderReportPeriodTypeEnum;
import lombok.Data;

/**
 * 统计报表
 * @TableName order_report
 */
@TableName(value ="order_report")
@Data
public class OrderReport extends BaseEntity {
    /**
     * 统计周期类型
     */
    @TableField(value = "period_type")
    private OrderReportPeriodTypeEnum period_type;

    /**
     * 周期值 (如 2025-09-17, 2025-09)
     */
    @TableField(value = "period_value")
    private String period_value;

    /**
     * 该周期内订单总数
     */
    @TableField(value = "total_orders")
    private Integer total_orders;

    /**
     * 该周期内订单总收入
     */
    @TableField(value = "total_revenue")
    private BigDecimal total_revenue;
}