package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入库记录表
 * @TableName stock_in
 */
@TableName(value ="stock_in")
@Data
@EqualsAndHashCode(callSuper = false)
public class StockIn extends BaseEntity {
    /**
     * 商品ID
     */
    @TableField(value = "goods_id")
    private Long goods_id;

    /**
     * 入库数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 入库单位成本
     */
    @TableField(value = "unit_price")
    private BigDecimal unit_price;

    /**
     * 供应商ID
     */
    @TableField(value = "supplier_id")
    private Long supplier_id;

    /**
     * 操作员ID
     */
    @TableField(value = "user_id")
    private Long user_id;
}