package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.vmbs.enums.PermissionMenuType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 权限表
 * @TableName permission
 */
@TableName(value ="permission")
@Data
@EqualsAndHashCode(callSuper = false)
public class Permission extends BaseEntity {
    /**
     * 权限名称
     */
    @TableField(value = "permission_name")
    private String permission_name;

    /**
     * 父权限ID (0表示顶级)
     */
    @TableField(value = "parent_id")
    private Integer parent_id;

    /**
     * 显示顺序
     */
    @TableField(value = "order_num")
    private Integer order_num;

    /**
     * 路由地址
     */
    @TableField(value = "path")
    private String path;

    /**
     * 组件路径
     */
    @TableField(value = "component")
    private String component;

    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @TableField(value = "menu_type")
    private PermissionMenuType menu_type;

    /**
     * 权限标识
     */
    @TableField(value = "perms")
    private String perms;

    /**
     * 权限图标
     */
    @TableField(value = "permission_icon")
    private String permission_icon;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;


    //数据库表中没有该字段，所以加了@TableField(exist = false)
    @TableField(exist = false)
    private List<Permission> children=new ArrayList<>();

}