package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 车辆表
 * @TableName car
 */
@TableName(value ="car")
@Data
public class Car extends BaseEntity {
    /**
     * 车牌号
     */
    @TableField(value = "license_plate")
    private String license_plate;

    /**
     * 车架号
     */
    @TableField(value = "vin")
    private String vin;

    /**
     * 品牌 (如: 丰田)
     */
    @TableField(value = "make")
    private String make;

    /**
     * 型号 (如: 凯美瑞)
     */
    @TableField(value = "model")
    private String model;

    /**
     * 年份
     */
    @TableField(value = "car_year")
    private Integer car_year;

    /**
     * 颜色
     */
    @TableField(value = "color")
    private String color;

    /**
     * 车辆类型值 (关联字典表item_value)
     */
    @TableField(value = "car_type")
    private String car_type;

    /**
     * 主要联系人ID
     */
    @TableField(value = "customer_id")
    private Long customer_id;

    /**
     * 所属公司ID (NULL表示个人车辆)
     */
    @TableField(value = "owner_company_id")
    private Long owner_company_id;
}