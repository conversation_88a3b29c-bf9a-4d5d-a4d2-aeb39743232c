package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.vmbs.enums.CouponStatusEnum;
import lombok.Data;

/**
 * 优惠券表
 * @TableName coupon
 */
@TableName(value ="coupon")
@Data
public class Coupon extends BaseEntity {
    /**
     * 所属客户ID
     */
    @TableField(value = "customer_id")
    private Long customer_id;

    /**
     * 金额
     */
    @TableField(value = "value")
    private BigDecimal value;

    /**
     * 最低消费金额
     */
    @TableField(value = "min_spend")
    private BigDecimal min_spend;

    /**
     * 状态
     */
    @TableField(value = "status")
    private CouponStatusEnum status;

    /**
     * 过期时间
     */
    @TableField(value = "expire_time")
    private Date expire_time;
}