package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 字典表
 * @TableName dictionary_item
 */
@TableName(value ="dictionary_item")
@Data
public class DictionaryItem extends BaseEntity {
    /**
     * 字典编码, 如 "CAR_TYPE"
     */
    @TableField(value = "dict_code")
    private String dict_code;

    /**
     * 显示名称, 如 "燃油车"
     */
    @TableField(value = "item_name")
    private String item_name;

    /**
     * 存储值
     */
    @TableField(value = "item_value")
    private String item_value;

    /**
     * 排序
     */
    @TableField(value = "sort_order")
    private Integer sort_order;
}