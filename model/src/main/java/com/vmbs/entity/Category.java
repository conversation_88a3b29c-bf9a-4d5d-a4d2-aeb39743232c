package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品分类表
 * @TableName category
 */
@TableName(value ="category")
@Data
@EqualsAndHashCode(callSuper = false)
public class Category extends BaseEntity {
    /**
     * 商品分类名称
     */
    @TableField(value = "category_name")
    private String category_name;
}