package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 积分变动记录表
 * @TableName points_log
 */
@TableName(value ="points_log")
@Data
public class PointsLog extends BaseEntity {
    /**
     * 客户ID
     */
    @TableField(value = "customer_id")
    private Long customer_id;

    /**
     * 关联订单ID
     */
    @TableField(value = "order_id")
    private Long order_id;

    /**
     * 积分变化 (正数为增加, 负数为兑换)
     */
    @TableField(value = "points_change")
    private Integer points_change;

    /**
     * 变动说明
     */
    @TableField(value = "description")
    private String description;
}