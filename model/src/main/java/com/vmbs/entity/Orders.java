package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.vmbs.enums.OrdersPaymentMethodEnum;
import com.vmbs.enums.OrdersStatusEnum;
import lombok.Data;

/**
 * 订单表
 * @TableName orders
 */
@TableName(value ="orders")
@Data
public class Orders extends BaseEntity {
    /**
     * 客户ID
     */
    @TableField(value = "customer_id")
    private Long customer_id;

    /**
     * 关联车辆ID
     */
    @TableField(value = "car_id")
    private Long car_id;

    /**
     * 开单员工ID
     */
    @TableField(value = "user_id")
    private Long user_id;

    /**
     * 订单总金额
     */
    @TableField(value = "total_price")
    private BigDecimal total_price;

    /**
     * 优惠总额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discount_amount;

    /**
     * 实付总额
     */
    @TableField(value = "final_amount")
    private BigDecimal final_amount;

    /**
     * 支付方式
     */
    @TableField(value = "payment_method")
    private OrdersPaymentMethodEnum payment_method;

    /**
     * 订单状态
     */
    @TableField(value = "status")
    private OrdersStatusEnum status;

    /**
     * 完单时间
     */
    @TableField(value = "end_time")
    private Date end_time;
}