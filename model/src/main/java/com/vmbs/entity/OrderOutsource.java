package com.vmbs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.vmbs.enums.OrderOutsourceStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 外包订单表
 * @TableName order_outsource
 */
@TableName(value ="order_outsource")
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderOutsource extends BaseEntity {
    /**
     * 主订单ID
     */
    @TableField(value = "order_id")
    private Long order_id;

    /**
     * 外包类型ID (关联字典表)
     */
    @TableField(value = "od_type_id")
    private Integer od_type_id;

    /**
     * 外包金额
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 外包供应商ID
     */
    @TableField(value = "supplier_id")
    private Long supplier_id;

    /**
     * 状态
     */
    @TableField(value = "status")
    private OrderOutsourceStatusEnum status;
}