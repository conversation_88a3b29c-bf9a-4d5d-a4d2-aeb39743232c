package com.vmbs.enums;

public enum OrderReportPeriodTypeEnum {
    DAY("day","日"),
    MONTH("month","月"),
    QUARTER("quarter","季度"),
    YEAR("year","年");

    private final String value;
    private final String description;

    OrderReportPeriodTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
