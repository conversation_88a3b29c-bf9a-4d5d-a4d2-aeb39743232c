package com.vmbs.enums;

public enum GoodsStatusEnum {
    ON_SHELF("on_shelf","上架"),
    OFF_SHELF("off_shelf","下架");

    private final String value;
    private final String description;

    GoodsStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
