package com.vmbs.enums;

public enum OrdersPaymentMethodEnum {
    CASH("cash","现金"),
    CARD("card","刷卡"),
    WECHAT("wechat","微信"),
    ALIPAY("alipay","支付宝"),
    BALANCE("balance","余额");

    private final String value;
    private final String description;

    OrdersPaymentMethodEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
