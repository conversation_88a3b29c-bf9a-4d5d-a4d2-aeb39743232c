package com.vmbs.enums;

public enum OrderOutsourceStatusEnum {
    IN_PROGRESS("in_progress","进行中"),
    COMPLETED("completed","已完成");

    private final String value;
    private final String description;

    OrderOutsourceStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
