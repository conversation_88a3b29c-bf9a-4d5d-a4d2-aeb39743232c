package com.vmbs.enums;

public enum OrdersStatusEnum {
    PENDING_PAYMENT("pending_payment","待支付"),
    PAID("paid","已支付"),
    IN_SERVICE("in_service","服务中"),
    COMPLETED("completed","已完成"),
    CANCELLED("cancelled","已取消");

    private final String value;
    private final String description;

    OrdersStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
