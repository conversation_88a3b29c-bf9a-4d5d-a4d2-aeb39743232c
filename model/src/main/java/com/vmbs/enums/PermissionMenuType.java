package com.vmbs.enums;

public enum PermissionMenuType {
    M("menu","目录"),
    C("catalog","菜单"),
    F("button","按钮");

    private final String value;
    private final String description;

    PermissionMenuType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
