package com.vmbs.enums;

public enum UserStatusEnum {
    ENABLED("enabled","正常"),
    DISABLED("disabled","禁用");

    private final String value;
    private final String description;

    UserStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
