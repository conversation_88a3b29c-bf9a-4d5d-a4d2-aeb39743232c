/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-18 23:35
 */

import com.vmbs.AdminWebApplication;
import com.vmbs.entity.Permission;
import com.vmbs.entity.Role;
import com.vmbs.entity.User;
import com.vmbs.mapper.PermissionMapper;
import com.vmbs.mapper.RoleMapper;
import com.vmbs.mapper.UserMapper;
import com.vmbs.service.PermissionService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Comparator;
import java.util.List;

@SpringBootTest(classes = AdminWebApplication.class) // 指定主应用类
@ExtendWith(SpringExtension.class)
public class AdminAppTest {

    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private UserMapper userMapper;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private PermissionMapper permissionMapper;
    @Resource
    private PermissionService permissionService;

    @Test
    public void testPasswordEncoder() {
        // $2a$10$M4bZN5vkBQtl4M4PWoUj8e0H7j.MBW3SWSoVhNBLruIGePrM9Nwti
        String password = "123456";
        String encodedPassword = passwordEncoder.encode(password);
        System.out.println(encodedPassword);
    }

    @Test
    public void testUserPermission(){
        User user =userMapper.selectByUsername("admin");
        //获取角色列表
        List<Role> roleList = roleMapper.selectRoleByUserId(Math.toIntExact(user.getId()));
        //获取权限列表
        List<Permission> permissionList = permissionMapper.selectPermissionByRoleId(roleList);
        //权限按照order_num的值进行排序
        permissionList.sort(Comparator.comparing(Permission::getOrder_num));
        //构建菜单目录
        List<Permission> menuList = permissionService.buildTreePermission(permissionList);
        for (Permission permission : menuList) {
            System.out.println(permission.getPermission_name());
            List<Permission> children = permission.getChildren();
            for (Permission child : children) {
                System.out.println("\t" + child.getPermission_name());
            }
        }
    }

}