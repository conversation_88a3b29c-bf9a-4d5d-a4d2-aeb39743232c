<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.PointsLogMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.PointsLog">
            <id property="id" column="id" />
            <result property="customer_id" column="customer_id" />
            <result property="order_id" column="order_id" />
            <result property="points_change" column="points_change" />
            <result property="description" column="description" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,order_id,points_change,description,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
