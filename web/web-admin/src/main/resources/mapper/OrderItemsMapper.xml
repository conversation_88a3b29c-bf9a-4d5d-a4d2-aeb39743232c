<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.OrderItemsMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.OrderItems">
            <id property="id" column="id" />
            <result property="order_id" column="order_id" />
            <result property="goods_id" column="goods_id" />
            <result property="item_type" column="item_type" />
            <result property="quantity" column="quantity" />
            <result property="price" column="price" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,goods_id,item_type,quantity,price,
        created_at,updated_at,is_deleted
    </sql>
</mapper>
