<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.DictionaryItemMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.DictionaryItem">
            <id property="id" column="id" />
            <result property="dict_code" column="dict_code" />
            <result property="item_name" column="item_name" />
            <result property="item_value" column="item_value" />
            <result property="sort_order" column="sort_order" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,dict_code,item_name,item_value,sort_order,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
