<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.SupplierMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Supplier">
            <id property="id" column="id" />
            <result property="supplier_name" column="supplier_name" />
            <result property="contact_person" column="contact_person" />
            <result property="phone" column="phone" />
            <result property="address" column="address" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_name,contact_person,phone,address,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
