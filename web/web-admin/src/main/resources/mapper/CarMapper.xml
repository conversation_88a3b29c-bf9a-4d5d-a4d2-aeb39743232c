<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.CarMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Car">
            <id property="id" column="id" />
            <result property="license_plate" column="license_plate" />
            <result property="vin" column="vin" />
            <result property="make" column="make" />
            <result property="model" column="model" />
            <result property="car_year" column="car_year" />
            <result property="color" column="color" />
            <result property="car_type" column="car_type" />
            <result property="customer_id" column="customer_id" />
            <result property="owner_company_id" column="owner_company_id" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,license_plate,vin,make,model,car_year,
        color,car_type,customer_id,owner_company_id,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
