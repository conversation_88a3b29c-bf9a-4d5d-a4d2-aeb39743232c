<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.PermissionMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Permission">
        <id property="id" column="id" />
        <result property="permission_name" column="permission_name" />
        <result property="parent_id" column="parent_id" />
        <result property="order_num" column="order_num" />
        <result property="path" column="path" />
        <result property="component" column="component" />
        <result property="menu_type" column="menu_type" />
        <result property="perms" column="perms" />
        <result property="description" column="description" />
        <result property="permission_icon" column="permission_icon" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>


    <sql id="Base_Column_List">
        id,permission_name,parent_id,order_num,path,component,
        menu_type,perms,description,created_at,updated_at,
        is_deleted
    </sql>

    <!--根据角色id查询权限信息-->
    <select id="selectPermissionByRoleId" resultMap="BaseResultMap">
        select p.*
        from permission p
        inner join role_permission rp on p.id = rp.permission_id
        where rp.role_id in
        <foreach collection="roleList" item="role" open="(" close=")" separator=",">
            #{role.id}
        </foreach>
    </select>


</mapper>