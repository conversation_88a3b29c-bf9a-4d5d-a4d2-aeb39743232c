<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Role">
            <id property="id" column="id" />
            <result property="role_name" column="role_name" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,role_name,created_at,updated_at,is_deleted
    </sql>

    <!--根据用户id查询用户的角色信息-->
    <select id="selectRoleByUserId" resultMap="BaseResultMap">
        select r.*
        from role r
                 inner join user u on u.role_id = r.id
        where u.id = #{userId}
    </select>

</mapper>
