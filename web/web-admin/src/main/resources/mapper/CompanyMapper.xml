<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.CompanyMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Company">
            <id property="id" column="id" />
            <result property="company_name" column="company_name" />
            <result property="tax_id" column="tax_id" />
            <result property="address" column="address" />
            <result property="contact_person" column="contact_person" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,company_name,tax_id,address,contact_person,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
