<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.CategoryMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Category">
            <id property="id" column="id" />
            <result property="category_name" column="category_name" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,category_name,created_at,updated_at,is_deleted
    </sql>
</mapper>
