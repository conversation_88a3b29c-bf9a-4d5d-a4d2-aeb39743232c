<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.OrdersMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Orders">
            <id property="id" column="id" />
            <result property="customer_id" column="customer_id" />
            <result property="car_id" column="car_id" />
            <result property="user_id" column="user_id" />
            <result property="total_price" column="total_price" />
            <result property="discount_amount" column="discount_amount" />
            <result property="final_amount" column="final_amount" />
            <result property="payment_method" column="payment_method" />
            <result property="status" column="status" />
            <result property="end_time" column="end_time" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,car_id,user_id,total_price,discount_amount,
        final_amount,payment_method,status,end_time,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
