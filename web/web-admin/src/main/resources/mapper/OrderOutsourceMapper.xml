<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.OrderOutsourceMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.OrderOutsource">
            <id property="id" column="id" />
            <result property="order_id" column="order_id" />
            <result property="od_type_id" column="od_type_id" />
            <result property="price" column="price" />
            <result property="supplier_id" column="supplier_id" />
            <result property="status" column="status" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,od_type_id,price,supplier_id,status,
        created_at,updated_at,is_deleted
    </sql>
</mapper>
