<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.CouponMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Coupon">
            <id property="id" column="id" />
            <result property="customer_id" column="customer_id" />
            <result property="value" column="value" />
            <result property="min_spend" column="min_spend" />
            <result property="status" column="status" />
            <result property="expire_time" column="expire_time" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,value,min_spend,status,expire_time,
        created_at,updated_at,is_deleted
    </sql>
</mapper>
