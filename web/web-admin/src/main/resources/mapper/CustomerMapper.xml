<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.CustomerMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Customer">
            <id property="id" column="id" />
            <result property="customer_name" column="customer_name" />
            <result property="phone" column="phone" />
            <result property="email" column="email" />
            <result property="customer_avatar" column="customer_avatar" />
            <result property="balance" column="balance" />
            <result property="points" column="points" />
            <result property="status" column="status" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_name,phone,email,customer_avatar,balance,
        points,status,created_at,updated_at,is_deleted
    </sql>
</mapper>
