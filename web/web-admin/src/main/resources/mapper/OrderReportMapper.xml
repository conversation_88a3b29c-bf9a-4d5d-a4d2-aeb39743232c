<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.OrderReportMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.OrderReport">
            <id property="id" column="id" />
            <result property="period_type" column="period_type" />
            <result property="period_value" column="period_value" />
            <result property="total_orders" column="total_orders" />
            <result property="total_revenue" column="total_revenue" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,period_type,period_value,total_orders,total_revenue,created_at,
        updated_at,is_deleted
    </sql>
</mapper>
