<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.StockInMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.StockIn">
            <id property="id" column="id" />
            <result property="goods_id" column="goods_id" />
            <result property="quantity" column="quantity" />
            <result property="unit_price" column="unit_price" />
            <result property="supplier_id" column="supplier_id" />
            <result property="user_id" column="user_id" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,goods_id,quantity,unit_price,supplier_id,user_id,
        created_at,updated_at,is_deleted
    </sql>
</mapper>
