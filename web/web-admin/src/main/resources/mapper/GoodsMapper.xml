<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.GoodsMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.Goods">
            <id property="id" column="id" />
            <result property="goods_name" column="goods_name" />
            <result property="sku" column="sku" />
            <result property="goods_pic" column="goods_pic" />
            <result property="description" column="description" />
            <result property="stock" column="stock" />
            <result property="price" column="price" />
            <result property="cost_price" column="cost_price" />
            <result property="category_id" column="category_id" />
            <result property="brand_id" column="brand_id" />
            <result property="status" column="status" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,goods_name,sku,goods_pic,description,stock,
        price,cost_price,category_id,brand_id,status,
        created_at,updated_at,is_deleted
    </sql>
</mapper>
