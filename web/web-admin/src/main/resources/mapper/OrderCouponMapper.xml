<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.OrderCouponMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.OrderCoupon">
            <id property="id" column="id" />
            <result property="order_id" column="order_id" />
            <result property="coupon_id" column="coupon_id" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,coupon_id,created_at,updated_at,is_deleted
    </sql>
</mapper>
