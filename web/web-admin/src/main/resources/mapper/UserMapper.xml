<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vmbs.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.vmbs.entity.User">
            <id property="id" column="id" />
            <result property="username" column="username" />
            <result property="password" column="password" />
            <result property="phone" column="phone" />
            <result property="email" column="email" />
            <result property="userAvatar" column="user_avatar" />
            <result property="roleId" column="role_id" />
            <result property="status" column="status" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,username,password,phone,email,user_avatar,
        role_id,status,created_at,updated_at,is_deleted
    </sql>

    <!--用自己的账号登录-->
    <select id="selectByUsername" resultType="com.vmbs.entity.User">
        select
            *
        from user
        where username = #{username}
          and is_deleted = 0
    </select>


</mapper>
