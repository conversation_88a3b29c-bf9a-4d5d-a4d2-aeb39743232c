package com.vmbs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vmbs.entity.Permission;
import com.vmbs.mapper.PermissionMapper;
import com.vmbs.service.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【permission(权限表)】的数据库操作Service实现
* @createDate 2025-09-18 19:03:22
*/
@Slf4j
@Service
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission>
    implements PermissionService{

    @Override
    public List<Permission> buildTreePermission(List<Permission> permissionList) {
        //1.创建一个List容器（装顶层菜单容器）
        List<Permission> topList =new ArrayList<>();
        //2.遍历
        for (Permission parent : permissionList) {
            for (Permission son : permissionList) {
                // 使用 longValue() 进行比较，避免Integer.equals(Long)始终为false的bug
                if(son.getParent_id() != null && son.getParent_id().longValue() == parent.getId()){
                    parent.getChildren().add(son);
                }
            }
            if(parent.getParent_id() == 0){
                //顶层菜单
                topList.add(parent);
            }
        }
        return topList;
    }


}


