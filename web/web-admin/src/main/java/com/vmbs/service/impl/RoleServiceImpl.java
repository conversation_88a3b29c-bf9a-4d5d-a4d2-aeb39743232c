package com.vmbs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vmbs.entity.Role;
import com.vmbs.mapper.RoleMapper;
import com.vmbs.service.RoleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【role(角色表)】的数据库操作Service实现
* @createDate 2025-09-17 19:55:30
*/
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role>
    implements RoleService{

    @Resource
    private RoleMapper roleMapper;

    @Override
    public List<Role> getRoleListByUserId(Integer userId) {
        return roleMapper.selectRoleByUserId(userId);
    }

}




