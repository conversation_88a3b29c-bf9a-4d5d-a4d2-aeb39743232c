package com.vmbs.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vmbs.entity.Permission;
import com.vmbs.entity.Role;
import com.vmbs.entity.User;
import com.vmbs.mapper.PermissionMapper;
import com.vmbs.mapper.RoleMapper;
import com.vmbs.mapper.UserMapper;
import com.vmbs.service.UserService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Service实现
* @createDate 2025-09-17 19:55:30
*/

@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User>
    implements UserService{

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public String getUserAuthorityInfo(Integer userId) {
        //1.创建拼接字符串StringBuffer
        StringBuffer buffer = new StringBuffer();

        //2.根据userId查询这个用户拥有的角色
        log.info("即将根据用户ID查询角色, userId: {}", userId);
        List<Role> roleList = roleMapper.selectRoleByUserId(userId);
        log.info("用户ID为{},拥有角色:{}",userId,roleList);
        if(Objects.nonNull(roleList) && !roleList.isEmpty()){
            String roleStr= roleList.stream()
                    .filter(role -> role != null && StrUtil.isNotEmpty(role.getRole_name()))
                    .map(role->"ROLE_"+role.getRole_name())
                    .collect(Collectors.joining(","));
            //将roleStr追加到buffer中 ROLE_common,ROLE_test
            if(StrUtil.isNotEmpty(roleStr)){
                buffer.append(roleStr);
            }
        }

        //3.准备一个set集合，存储所有的权限名称
        Set<String> menuSet =new LinkedHashSet<>();
        // 只有在有角色的情况下才查询权限
        if(Objects.nonNull(roleList) && !roleList.isEmpty()){
            List<Permission> sysMenuSet = permissionMapper.selectPermissionByRoleId(roleList);
            log.info("用户id为{}，拥有权限:{}",userId,sysMenuSet);
            if(Objects.nonNull(sysMenuSet)){
                for(Permission permission:sysMenuSet){
                    String perms = permission.getPerms();
                    if(StrUtil.isNotEmpty(perms)){
                        menuSet.add(perms);
                    }
                }
            }
        }
        //log.info("menuSet={}",menuSet);
        if(menuSet.size()>0){
            if(buffer.length() > 0){
                buffer.append(",");
            }
            String menuStr = menuSet.stream().collect(Collectors.joining(","));
            buffer.append(menuStr);
        }

        log.info("userId为{}拥有角色和权限字符串为:{}",userId,buffer.toString());
        return buffer.toString();
    }




}