package com.vmbs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vmbs.entity.Permission;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【permission(权限表)】的数据库操作Service
* @createDate 2025-09-18 19:03:22
*/
public interface PermissionService extends IService<Permission> {

    //左侧动态权限菜单的实现
    List<Permission> buildTreePermission(List<Permission> permissionList);

}
