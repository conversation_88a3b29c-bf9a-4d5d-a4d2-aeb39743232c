package com.vmbs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vmbs.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【role(角色表)】的数据库操作Mapper
* @createDate 2025-09-17 19:55:30
* @Entity com.vmbs.entity.Role
*/
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    //根据用户id查询用户的角色信息
    List<Role> selectRoleByUserId(@Param("userId") Integer userId);

}




