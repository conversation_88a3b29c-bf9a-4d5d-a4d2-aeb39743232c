package com.vmbs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vmbs.entity.Permission;
import com.vmbs.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【permission(权限表)】的数据库操作Mapper
* @createDate 2025-09-18 19:03:22
* @Entity com.vmbs.entity.Permission
*/
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    //根据角色id查询权限信息
    List<Permission> selectPermissionByRoleId(@Param("roleList") List<Role> roleList);

}




