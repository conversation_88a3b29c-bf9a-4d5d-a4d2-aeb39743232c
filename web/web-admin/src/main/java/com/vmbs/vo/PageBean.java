package com.vmbs.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-22 20:22
 */
@Data
public class PageBean {
    /**页码*/
    private Integer pageNum;
    /**每页显示记录数*/
    private Integer pageSize;
    /**从几条数据开始 pageSize*(pageNum-1)*/
    private Integer start;
    /**查询条件*/
    private String query;

    public PageBean() {
    }

    public PageBean(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public PageBean(Integer pageNum, Integer pageSize, String query) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.query = query;
    }

}
