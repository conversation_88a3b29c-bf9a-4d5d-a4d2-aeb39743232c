package com.vmbs.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-22 11:21
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private LoginSuccessHandler loginSuccessHandler;

    @Autowired
    private LoginFailureHandler loginFailureHandler;

    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    @Autowired
    private TokenAuthenticationEntryPoint tokenAuthenticationEntryPoint;

    @Autowired
    private CustomLogoutSuccessHandler customLogoutSuccessHandler;

    private static final String[] PASSLIST = {"/login", "/logout", "/images/**", "/test/**",
            "/doc.html", "/webjars/**", "/v3/api-docs/**"};

    @Bean
    public TokenAuthenticationFilter tokenAuthenticationFilter(TokenAuthenticationEntryPoint tokenAuthenticationEntryPoint) {
        return new TokenAuthenticationFilter(tokenAuthenticationEntryPoint);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {


        //开启跨域
        http.cors((cors)->{})
                //禁用csrf保护
                .csrf((csrf)->csrf.disable())

                //添加自定义过滤器
                .addFilterBefore(tokenAuthenticationFilter(tokenAuthenticationEntryPoint), UsernamePasswordAuthenticationFilter.class)

                //设置会话创建策略为无状态
                .sessionManagement(session->
                        session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                //用户详情服务
                .userDetailsService(customUserDetailsService)
                //登录配置
                .formLogin(form->{
                    form.usernameParameter("username")
                            .passwordParameter("password")
                    .successHandler(loginSuccessHandler)
                            .failureHandler(loginFailureHandler);
                })
                .exceptionHandling(exceptionHandling->{
                    exceptionHandling.authenticationEntryPoint(tokenAuthenticationEntryPoint);
                })
                .logout(logout->{
                    logout.logoutSuccessHandler(customLogoutSuccessHandler);
                })
                //配置拦截规则
                .authorizeHttpRequests(authz -> authz
                        .requestMatchers(PASSLIST).permitAll() //设置放行路径
                        .anyRequest().authenticated() //其他接口需要登录认证
                );
        //构建并返回安全过滤器链
        return http.build();
    }

    // Spring Security 提供的 BCryptPasswordEncoder 来加密用户密码
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 跨域配置
     */
    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowedHeaders(Collections.singletonList("*"));
        corsConfiguration.setAllowedMethods(Collections.singletonList("*"));
        corsConfiguration.setAllowedOrigins(Collections.singletonList("*"));
        corsConfiguration.setMaxAge(3600L);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        return source;
    }


}