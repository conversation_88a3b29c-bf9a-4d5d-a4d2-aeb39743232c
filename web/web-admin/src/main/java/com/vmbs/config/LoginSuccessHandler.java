package com.vmbs.config;

import cn.hutool.json.JSONUtil;
import com.vmbs.result.Result;
import com.vmbs.util.JwtUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
* <AUTHOR>
* @version 1.0
* @date 2025-09-22 11:24
*/
@Component // 交给IOC容器管理
@Slf4j // 日志
public class LoginSuccessHandler implements AuthenticationSuccessHandler {

    //一旦登录成功，就会调用这个方法
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        //生成token值，将值传递给前端vue项目
        //<1> 设置响应数据格式
        response.setContentType("application/json;charset=utf-8");
        //<2>获取已经认证成功的用户
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String username = userDetails.getUsername();
        log.info("认证成功的用户名:{}",username);
        //<3>根据用户名创建token值
        Map<String, String> claims = new HashMap<>();
        claims.put("username", username);
        String token = JwtUtil.generateToken("mySecret","vmbs","userAuth",claims);
        //<4> 封装传递给前端vue项目的对象
        Result<String> r = Result.ok(200, "登录成功", token);
        //<5> 将对象转换成json格式字符串
        String jsonStr = JSONUtil.toJsonStr(r);
        //<6> 使用字符输出流传递数据
        response.getWriter().write(jsonStr);
    }
}
