package com.vmbs.config;

import cn.hutool.core.util.StrUtil;
import com.vmbs.enums.UserStatusEnum;
import com.vmbs.mapper.UserMapper;
import com.vmbs.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.springframework.security.core.userdetails.User.withUsername;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-22 11:44
 */
@Slf4j
@Service //交给IOC容器管理
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    // UserDetails 接口
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        //1.去mysql数据库中查询用户信息
        com.vmbs.entity.User sysUser = userMapper.selectByUsername(username);
        log.info("当前用户信息:{}",sysUser);
        if(Objects.isNull(sysUser)){
            throw new UsernameNotFoundException("账户不存在!");
        }else if(sysUser.getStatus() == UserStatusEnum.DISABLED){
            throw new LockedException("账号被锁定,请联系超级管理员!");
        }
        //给这个用户授予权限
        Integer userId = Math.toIntExact(sysUser.getId());
        List<GrantedAuthority> grantedAuthorities = getSysUserAuthority(userId);
        User.UserBuilder userBuilder = withUsername(sysUser.getUsername()); //设置用户名
        userBuilder.password(sysUser.getPassword());//设置密码
        userBuilder.authorities(grantedAuthorities); //设置这个用户拥有的权限
        return userBuilder.build();
    }

    /**
     * 根据用户id获取这个用户具有的权限
     * @param userId
     * @return
     */
    public List<GrantedAuthority> getSysUserAuthority(Integer userId){
        List<GrantedAuthority> list = new ArrayList<>();
        //目前，还没有做权限一块，所以这个集合中是空的，等下再去实现
        String authority = userService.getUserAuthorityInfo(userId);
        if(StrUtil.isNotEmpty(authority)){
            list = AuthorityUtils.createAuthorityList(authority);
        }
        return  list;
    }

}