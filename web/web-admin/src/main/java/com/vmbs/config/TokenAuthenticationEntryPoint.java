package com.vmbs.config;

import cn.hutool.json.JSONUtil;
import com.vmbs.result.Result;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-22 11:54
 */
@Slf4j
@Component
public class TokenAuthenticationEntryPoint implements AuthenticationEntryPoint {


    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException exception) throws IOException, ServletException {
        //把登录失败的原因传递给前端vue项目
        response.setContentType("application/json;charset=utf-8");
        //出现的异常信息
        String errorMessage=exception.getMessage();
        log.info("jwt解析异常处理器已执行，失败原因{}",errorMessage);
        //封装传递给前端vue项目的对象
        Result r = Result.fail(500, errorMessage);
        //将对象转换成json格式字符串
        String jsonStr = JSONUtil.toJsonStr(r);
        //使用字符输出流传递数据
        response.getWriter().write(jsonStr);
    }
}