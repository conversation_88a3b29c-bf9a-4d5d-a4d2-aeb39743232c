package com.vmbs.config;

import ch.qos.logback.core.util.StringUtil;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.vmbs.entity.User;
import com.vmbs.mapper.UserMapper;
import com.vmbs.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
* <AUTHOR>
* @version 1.0
* @date 2025-09-22 13:36
*/
@Slf4j
@Configuration
public class TokenAuthenticationFilter extends OncePerRequestFilter {
    private static final String SECRET = "mySecret";
    private static final String ISSUER = "vmbs";
    //不需要认证的接口
    private static final String[] PASSLIST = {"/login", "/logout", "/images/**", "/test/**",
            "/doc.html", "/webjars/**", "/v3/api-docs/**"};
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    //传入token认证失败处理器
    private TokenAuthenticationEntryPoint tokenAuthenticationEntryPoint;

    //有参构造方法
    public TokenAuthenticationFilter(TokenAuthenticationEntryPoint tokenAuthenticationEntryPoint) {
        this.tokenAuthenticationEntryPoint = tokenAuthenticationEntryPoint;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        try {
            //1.获取请求头的 token
            String header = request.getHeader("token");
            log.info("header={}", header);
            //3.判断是否需要认证
            if (StringUtil.isNullOrEmpty(header)) {
                //3.1获取请求路径
                String uri = request.getRequestURI();
                log.info("uri={}", uri);
                //3.2判断是否需要认证
                List<String> list = Arrays.asList(PASSLIST);
                if (list.contains(uri) || uri.startsWith("/doc.html")|| uri.startsWith("/webjars") || uri.startsWith("/v3/api-docs")) {
                    filterChain.doFilter(request, response); //放行
                    return;
                } else {
                    throw new BadCredentialsException("jwt token为空，此请求路径需要认证!");
                }
            } else {
                //4.验证token值
                DecodedJWT jwt = null;
                try {
                    jwt = JwtUtil.verifyToken(header, SECRET, ISSUER);
                } catch (JWTVerificationException e) {
                    log.info("jwt解析异常");
                    throw new BadCredentialsException("jwt token认证失败!");
                }
                log.info("jwt={}", jwt);
                if (jwt != null) {
                    // 从token中获取用户信息
                    String username = JwtUtil.getClaimAsString(header, SECRET, ISSUER, "username");
                    log.info("解析token获取username={}", username);
                    //根据用户名获取用户详细信息
                    User user = userMapper.selectByUsername(username);
                    log.info("根据用户名获取用户详细信息={}", user);
                    //创建认证对象
                    //参数1：用户名
                    //参数2：密码
                    //参数3：权限
                    UsernamePasswordAuthenticationToken authentication =
                            new UsernamePasswordAuthenticationToken(username, null,
                                    customUserDetailsService.getSysUserAuthority(Math.toIntExact(user.getId())));
                    //将这个对象交给认证管理器 AuthenticationManager
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    //放行执行下一个过滤器
                    filterChain.doFilter(request, response);
                } else {
                    throw new BadCredentialsException("jwt token认证失败!");
                }
            }
        }catch (BadCredentialsException e){
            //交给认证失败处理器处理
            tokenAuthenticationEntryPoint.commence(request, response, e);
        }

    }
}
