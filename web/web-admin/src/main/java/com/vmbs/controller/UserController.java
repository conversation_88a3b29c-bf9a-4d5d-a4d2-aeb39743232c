package com.vmbs.controller;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vmbs.entity.Role;
import com.vmbs.entity.User;
import com.vmbs.result.Result;
import com.vmbs.service.RoleService;
import com.vmbs.service.UserService;
import com.vmbs.util.JwtUtil;
import com.vmbs.vo.PageBean;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "管理员用户相关功能接口")
@RestController
@Slf4j
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @PostMapping("/list")
    /**拥有查询权限才可以访问方法*/
    @PreAuthorize("hasAuthority('system:user:view')")
    public Result userList(@RequestBody PageBean pageBean) {
        log.info("分页查询用户列表,参数:{}", pageBean);
        //mybatis-plus提供一个page方法帮助分页
        Page<User> page = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<User> iPage = userService.page(page);
        //获取页面显示的数据
        List<User> userList = iPage.getRecords();
        //把每个用户拥有与角色查询出来
        for (User user : userList) {
            List<Role> roleList= roleService.getRoleListByUserId(Math.toIntExact(user.getId()));
            user.setRoleList(roleList);
        }
        //重置设置到iPage对象中
        iPage.setRecords(userList);
        return Result.ok(iPage);
    }

    @GetMapping("/{id}")
    public Result getUserById(@PathVariable("id") Integer id) {
        log.info("getUserById: {}", id);
        User user = userService.getById(id);
        return Result.ok(user);
    }



}