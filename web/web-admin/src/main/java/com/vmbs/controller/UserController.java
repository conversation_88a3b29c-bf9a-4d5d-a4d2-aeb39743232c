package com.vmbs.controller;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vmbs.entity.Role;
import com.vmbs.entity.User;
import com.vmbs.result.Result;
import com.vmbs.service.RoleService;
import com.vmbs.service.UserService;
import com.vmbs.util.JwtUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "管理员用户相关功能接口")
@RestController
@Slf4j
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;


    @PostMapping("/login")
    public Result test() {
        Map<String, String> claims = new HashMap<>();
        claims.put("userId", "1");
        claims.put("username", "admin");
        String token = JwtUtil.generateToken("mySecret","vmbs","userAuth",claims);
        return Result.ok(token);
    }

    @GetMapping("/list")
    public Result list(@RequestHeader(required = false)String token) {
        if (token != null) {
            // 验证Token
            try {
                DecodedJWT jwt = JwtUtil.verifyToken(token,"mySecret","vmbs");
                return Result.ok("Token验证成功");
            } catch (JWTVerificationException e) {
                e.printStackTrace();
                return Result.fail(500,"Token验证失败");
            }
        }
        return Result.fail(500,"Token验证失败");
    }

    @GetMapping("/{id}")
    public Result getUserById(@PathVariable("id") Integer id) {
        log.info("getUserById: {}", id);
        User user = userService.getById(id);
        return Result.ok(user);
    }



}