<template>
  <div class="user-info-box">
    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%">
      <!-- prop User中的属性名 -->
      <!-- label 表格列名 -->
      <!-- width 定义列的宽度 -->
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="编号" width="55" />
      <el-table-column prop="username" label="用户名" width="100" />
      <el-table-column prop="real_name" label="真实姓名" width="100" />
      <!--显示头像-->
      <el-table-column prop="avatar_url" label="用户头像" width="80" align="center">
        <!--template（模版） 在这里属于一个固定用法:--->
        <!--scope.row 拿到当前行的数据对象-->
        <template v-slot="scope">
          <img :src="scope.row.avatar_url" width="60" height="60" />
        </template>
      </el-table-column>
      <!--显示拥有的角色-->
      <el-table-column prop="roleList" label="拥有角色" width="220" align="center">
        <template v-slot="scope">
          <!-- Role实体类属性名一致 -->
          <el-tag size="small" type="success" v-for="role in scope.row.roleList" :key="role.id">
            {{ role.role_name }}
          </el-tag>
        </template>
      </el-table-column>
      <!--手机号-->
      <el-table-column prop="phone_number" label="手机号" width="120" align="center" />
      <!--状态-->
      <el-table-column prop="status" label="状态" width="150" align="center">
        <template v-slot="{ row }">
          <el-switch v-model="row.status" active-text="正常" inactive-text="禁用" active-value="0" size="small"
                     inactive-value="1" @change="statusChangeHandle(row)" active-color="#13ce66"
                     inactive-color="#ff4949" />
        </template>
      </el-table-column>
      <!--操作-->
      <el-table-column prop="action" label="操作" width="300" align="center" fixed="right">
        <template v-slot="{ row }">
          <el-button type="primary" :icon="Edit" size="small">编辑</el-button>
          <el-button type="danger" :icon="Delete" size="small">删除</el-button>
          <el-button type="primary" :icon="Avatar" size="small">分配角色</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 @size-change修改pageSize触发事件 -->
    <!--  @current-change 修改pageNum触发事件-->
    <!-- current-page 当前页码 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                   :current-page="pageBean.pageNum" :page-sizes="[2, 3, 4, 5]" :page-size="pageBean.pageSize"
                   layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination>
  </div>
</template>
<script setup>
import { Delete, Edit, Avatar, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, ref } from "vue"
import { getUserList } from "../../../api/user.js"
const tableData = ref([])
const pageBean = reactive({ pageNum: 1, pageSize: 2, query: "" })
const total = ref(0)

const initTableData = async () => {
  let { data } = await getUserList(pageBean);
  tableData.value = data.records;
  total.value = data.total;
  pageBean.pageNum = data.current;
  pageBean.pageSize = data.size;
}

onMounted(() => {
  initTableData();
})

const handleSizeChange = (val) => {
  //修改pageSize
  pageBean.pageNum = 1;
  pageBean.pageSize = val;
  //重新调用initTableData函数
  initTableData();
}

const handleCurrentChange = (val) => {
  pageBean.pageNum = val;
  initTableData();
}

const statusChangeHandle = (row) => {
  //点击开关触发的函数
}
</script>
<style lang="scss" scoped>
.user-info-box {
  padding-top: 30px;
}

// 修改element-ui组件的样式
.el-pagination {
  text-align: center;
  padding: 20px;
}
</style>