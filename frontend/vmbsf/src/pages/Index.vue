<template>
  <div class="index-container">
    <el-container>
      <el-aside width="200px" class="aside-container">
        <Menu />
      </el-aside>
      <el-container>
        <el-header>
          <Header />
        </el-header>
        <el-main>
          <Tabs />
        </el-main>
        <el-footer>
          <Footer />
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
//导入组件
import Header from "../components/Header.vue";
import Menu from "../components/Menu.vue";
import Footer from "../components/Footer.vue";
import Tabs from "../components/Tabs.vue";
</script>

<style lang="scss" scoped>
.index-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.aside-container {
  background-color: #2d3a4b;
  color: #ffffff;
  height: 100%;
}

.el-container {
  height: 100%;
}

.el-header,
.el-footer {
  padding: 0;
}
</style>
