<template>
  <!-- 卡片 -->
  <el-card class="box-card">
    <!-- 卡片 -->
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <span>如约婚纱摄影后台系统</span>
        </div>
      </template>
      <!-- 表单开始 -->
      <el-form :model="form" label-width="auto" ref="ruleFormRef" :rules="rules">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密&nbsp;&nbsp;&nbsp;&nbsp;码" prop="password">
          <el-input type="password" v-model="form.password" placeholder="请输入密码" />
        </el-form-item>
        <!-- 记住密码功能 -->
        <el-form-item>
          <el-checkbox v-model="form.rememberMe" label="记住密码"></el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" class="button">登录</el-button>
          <el-button type="info" @click="resetForm" class="button">取消</el-button>
        </el-form-item>
      </el-form>
      <!-- 表单结束 -->
    </el-card>
  </el-card>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import { login } from "../api/user";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
import cookie from "js-cookie";
import { encrypt, decrypt } from "../utils/jsencrypt.js"
import { useTokenStore } from "../stores/token";
import { useMenuListStore } from "../stores/menuList";
import { useUserStore } from "../stores/user";
const tokenStore = useTokenStore();
const menuListStore = useMenuListStore();
const userStore = useUserStore();
const form = reactive({
  username: "",
  password: "",
  rememberMe: false,
});

//表单验证
const ruleFormRef = ref();
const rules = reactive({
  username: [
    { required: true, message: "请输入用户名!", trigger: "blur" },
    { min: 3, max: 12, message: "用户名长度为3~12字符!", trigger: "blur" },
  ],
  password: [
    { required: true, message: "请输入密码!", trigger: "blur" },
    { min: 6, max: 100, message: "用户名长度为6~100字符!", trigger: "blur" },
  ],
});

const getUsernameAndPasswordHash = () => {
  const flag = cookie.get("rememberMe");
  if (flag) {
    form.username = cookie.get("username");
    form.password = decrypt(cookie.get("password"));
    form.rememberMe = Boolean(flag);
  }
};

onMounted(() => {
  getUsernameAndPasswordHash();
});

//按钮执行的方法
//按钮执行的方法
const onSubmit = () => {
  //表单验证
  ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      //表单验证成功
      if (form.rememberMe) {
        //记住密码功能
        //加密
        //保存到cookie中
        cookie.set("username", form.username, { expires: 7 });
        cookie.set("password", encrypt(form.password), { expires: 7 });
        cookie.set("rememberMe", form.rememberMe, { expires: 7 });
      } else {
        //不记住密码功能
        //保存到cookie中
        cookie.remove("username");
        cookie.remove("password");
        cookie.remove("rememberMe");
      }

      const { code, message, data } = await login(form);
      // console.log(code, message, data);
      // data -->HashMap
      if (code === 200) {
        //把token值存入cookie中
        tokenStore.updateToken(data.jwtToken);
        menuListStore.updateMenuList(data.menuList);
        userStore.updateUser(data.user);
        //跳转到index页面
        router.push({ name: 'index' });
      } else {
        ElMessage.error(message);
      }
    }
  });
};
const resetForm = () => {
  //清空输入框中的值
  ruleFormRef.value.resetFields();
};
</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  height: 100vh;
  background: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%) no-repeat;
  position: relative;

  .form-card {
    width: 60%;
    max-width: 550px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .card-header {
      text-align: center;
      font-size: 18px;
    }

    .el-form {
      // 弹性盒子
      display: flex;
      // 子元素方向
      flex-direction: column;
      // 主轴对齐方式
      justify-content: center;
      // 侧轴对齐方式
      align-items: center;

      .el-form-item {
        width: 70%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        --el-form-label-font-size: 16px;
        margin-top: 15px;
        margin-bottom: 15px;

        .button {
          width: 90px;
          margin-left: 50px;
        }
      }
    }

    .card-footer {
      text-align: center;
      font-size: 16px;
      color: gray;
    }
  }
}
</style>
