import { createWebHistory, createRouter } from "vue-router";
import cookie from "js-cookie";
import { useHasRoutesStore } from "../stores/hasRoutes";
import { useMenuListStore } from "../stores/menuList";

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: "/",
            redirect: "/index",
        },
        {
            path: "/index",
            name: "index",
            component: () => import("../pages/Index.vue"),
            meta: { title: "主页面" },
            redirect: "/index/welcome",
            children: [
                {
                    path: "/index/welcome",
                    name: "welcome",
                    component: () => import("../pages/Welcome.vue"),
                    meta: { title: "欢迎页面" },
                },
                {
                    path: "/index/userCenter",
                    name: "userCenter",
                    component: () => import("../pages/UserCenter.vue"),
                    meta: { title: "个人中心" },
                },
            ],
        },
        {
            path: "/login",
            name: "login",
            component: () => import("../pages/Login.vue"),
            meta: { title: "登录" },
        },
    ],
});

// 动态绑定路由的方法
const bindDynamicRoutes = async () => {
    try {
        const hasRoutesStore = useHasRoutesStore();

        // 获取权限菜单
        const menuListStore = useMenuListStore();
        menuListStore.restoreMenuListInfo();
        const menuList = menuListStore.getMenuList;

        console.log("菜单列表:", menuList);

        if (menuList && menuList.length > 0) {
            // 遍历menuList
            for (let menu of menuList) {
                // 如果此menu的children不为空
                if (menu.children && menu.children.length > 0) {
                    // 遍历menu.children
                    for (let sonMenu of menu.children) {
                        // 根据子菜单动态创建路由
                        if (!sonMenu.component) {
                            // permission表中component列的值为空
                            continue;
                        } else {
                            // 创建动态路由
                            let route = {
                                path: sonMenu.path,
                                name: sonMenu.permission_name,
                                meta: {
                                    parentName: menu.permission_name,
                                    title: sonMenu.permission_name,
                                },
                            };

                            // 动态导入组件
                            try {
                                route.component = () =>
                                    import("../pages" + menu.path + sonMenu.component + ".vue");

                                // 检查路由是否已存在
                                const existingRoute = router.hasRoute(route.name);
                                if (!existingRoute) {
                                    // 把创建好的新路由添加到路由中
                                    router.addRoute("index", route);
                                    console.log("添加路由:", route.path);
                                }
                            } catch (importError) {
                                console.error("动态导入组件失败:", importError);
                            }
                        }
                    }
                }
            }

            // 更新动态路由绑定状态
            hasRoutesStore.updateHasRoutes(true);
            console.log("动态路由绑定完成");
            return true;
        }
    } catch (error) {
        console.error("动态路由绑定失败:", error);
        return false;
    }
};

// 设置路由的全局前置守卫
router.beforeEach(async (to, from, next) => {
    document.title = "如约婚纱摄影(" + (to.meta.title || "未知页面") + ")";

    console.log(`从哪里来:${from.path},到哪里去:${to.path}`);

    if (to.path === "/login") {
        next();
        return;
    }

    // 从cookie中获取token值
    console.log("从cookie中获取token值", cookie.get("token"));

    if (cookie.get("token")) {
        const hasRoutesStore = useHasRoutesStore();

        // 恢复路由绑定状态
        hasRoutesStore.restoreHasRoutes();

        // 判断是否已经动态绑定路由
        if (!hasRoutesStore.getHasRoutes) {
            // 绑定动态路由
            await bindDynamicRoutes();
        }

        // 检查目标路由是否存在
        const targetRoute = router.resolve(to);

        // 如果是动态路由且不存在，则重新绑定并重定向
        if (!targetRoute.name || targetRoute.name === "NotFound") {
            // 检查是否为动态路由路径
            const menuListStore = useMenuListStore();
            menuListStore.restoreMenuListInfo();
            const menuList = menuListStore.getMenuList;

            let isDynamicRoute = false;
            if (menuList && menuList.length > 0) {
                for (let menu of menuList) {
                    if (menu.children && menu.children.length > 0) {
                        for (let sonMenu of menu.children) {
                            if (sonMenu.path === to.path) {
                                isDynamicRoute = true;
                                break;
                            }
                        }
                        if (isDynamicRoute) break;
                    }
                }
            }

            // 如果是动态路由但找不到，重新绑定路由
            if (isDynamicRoute) {
                console.log("检测到动态路由，重新绑定:", to.path);
                const hasRoutesStore = useHasRoutesStore();
                hasRoutesStore.updateHasRoutes(false); // 强制重新绑定
                await bindDynamicRoutes();

                // 重新检查路由是否存在
                const reResolvedRoute = router.resolve(to);
                if (reResolvedRoute.name && reResolvedRoute.name !== "NotFound") {
                    next({ ...to, replace: true });
                    return;
                }
            }
        }

        next();
    } else {
        // 跳转到登录
        next("/login");
    }
});


// 动态绑定路由
const menuToRoute = (sonMenu,parentName,parentPath) => {
    if(!sonMenu.component) {
        return null;
    }else{
        let route = {
            path: sonMenu.path,
            name: sonMenu.permission_name,
            meta: {
                parentName: parentName,
            },
        };
        route.component = () =>
            import("../pages/"+parentPath+"/"+sonMenu.component+".vue");
        return route;
    }
};

// 设置路由的全局后置守卫
router.afterEach((to, from) => {
    console.log(`从哪里来:${from.path},到哪里去:${to.path}`);
});

// 对外暴露路由对象
export default router;
