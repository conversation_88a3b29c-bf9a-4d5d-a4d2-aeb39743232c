import { createWebHistory, createRouter } from "vue-router";
import cookie from "js-cookie";
const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: "/",
            redirect: "/index",
        },
        {
            path: "/index",
            name: "index",
            component: () => import("../pages/Index.vue"),
            meta: { title: "主页面" },
        },
        {
            path: "/login",
            name: "login",
            component: () => import("../pages/Login.vue"),
            meta: { title: "登录" },
        },
    ],
});
// 设置路由的全局前置守卫
router.beforeEach((to, from, next) => {
    /*
      to 要去那
      from 从哪里来
      next 放行路由时需要调用的方法,不调用则不放行
      */
    console.log(`从哪里来:${from.path},到哪里去:${to.path}`)
    if (to.path === '/login') {
        //放行路由  注意放行不要形成循环
        next()
    } else {
        //从cookie中获取token值
        console.log('从cookie中获取token值',cookie.get('token'))
        if (cookie.get('token')) {
            next()
        } else {
            //跳转到登录
            next('/login')
        }
    }
})
// 设置路由的全局后置守卫
router.afterEach((to, from) => {
    console.log(`从哪里来:${from.path},到哪里去:${to.path}`)
})
// 对外暴露路由对象
export default router;
