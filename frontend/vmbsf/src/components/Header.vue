<template>
  <div class="navbar">
    <Breadcrumb />
    <div class="navbar-right">
      <Avatar />
    </div>
  </div>
</template>

<script setup>
import Breadcrumb from "./header/Breadcrumb.vue";
import Avatar from "./header/Avatar.vue";
</script>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #f2f9ff;
  background-color: #f2f9ff;
  display: flex;
  /*弹性盒子*/
  flex-direction: row;
  /*主轴水平*/
  position: relative;
  box-sizing: border-box;

  .navbar-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    /*修改默认样式*/
    :deep(.navbar-item) {
      display: inline-block;
      margin-left: 15px;
      font-size: 100px;
      cursor: pointer;
    }
  }
}
</style>
