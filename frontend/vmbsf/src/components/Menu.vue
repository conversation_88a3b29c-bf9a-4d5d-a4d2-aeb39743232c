<template>
  <div class="menu-box">
    <!-- Logo 区域 -->
    <div class="logo-container">
      <span class="logo-title">如约婚纱摄影后台</span>
    </div>

    <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical-demo"
        router
        @select="handleSelect"
    >
      <template v-for="parentMenu in menuList" :key="parentMenu.id">
        <!-- 有子菜单的父级菜单 -->
        <el-sub-menu
            v-if="parentMenu.children && parentMenu.children.length > 0"
            :index="parentMenu.path"
        >
          <template #title>
            <el-icon>
              <component :is="getElIcon(parentMenu.permission_icon)" />
            </el-icon>
            <span>{{ parentMenu.permission_name }}</span>
          </template>

          <!-- 子菜单项 -->
          <el-menu-item
              v-for="sonMenu in parentMenu.children"
              :key="sonMenu.id"
              :index="sonMenu.path"
              @click="createTabBtn(sonMenu)"
          >
            <el-icon>
              <component :is="getElIcon(sonMenu.permission_icon)" />
            </el-icon>
            <span>{{ sonMenu.permission_name }}</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 无子菜单的菜单项 -->
        <el-menu-item v-else :index="parentMenu.path" @click="createTabBtn(parentMenu)">
          <el-icon>
            <component :is="getElIcon(parentMenu.permission_icon)" />
          </el-icon>
          <span>{{ parentMenu.permission_name }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { useMenuListStore } from "../stores/menuList";
import store from "../stores/index";

// ✅ 这是你要保留的 Element Plus 图标组件集合
import {
  Menu as IconMenu,
  User,
  Histogram,
  Box,
  ShoppingBag,
  Setting,
  UserFilled,
  Hide,
  Management,
  ShoppingCart,
  TrendCharts,
  List,
  CirclePlusFilled,
  Search,
  Goods,
  Printer,
  OfficeBuilding,
  Van,
  StarFilled
} from "@element-plus/icons-vue";

const menuListStore = useMenuListStore();
const menuList = ref([]);
const route = useRoute();

// ✅ 图标映射表，全部用小写 key，保证数据库大小写无关
const elIcons = {
  menu: IconMenu,
  user: User,
  histogram: Histogram,
  box: Box,
  shoppingbag: ShoppingBag,
  setting: Setting,
  userfilled: UserFilled,
  hide: Hide,
  management: Management,
  shoppingcart: ShoppingCart,
  trendcharts: TrendCharts,
  list: List,
  circleplusfilled: CirclePlusFilled,
  search: Search,
  goods: Goods,
  printer: Printer,
  officebuilding: OfficeBuilding,
  van: Van,
  starfilled: StarFilled
};

/**
 * 获取 Element Plus 图标
 */
const getElIcon = (iconName) => {
  if (!iconName) return IconMenu;
  const iconComponent = elIcons[iconName.toLowerCase()];
  return iconComponent || IconMenu;
};

/**
 * 当前激活菜单
 */
const activeMenu = computed(() => route.path);

/**
 * 选中菜单
 */
const handleSelect = (index) => {
  console.log("选中菜单:", index);
};

/**
 * 创建 Tab
 */
const createTabBtn = (menu) => {
  store.commit("ADD_TABS", { name: menu.permission_name, path: menu.path });
  store.commit("SET_DEFAULT_TAB_VALUE", menu.path);
};

/**
 * 加载菜单
 */
const loadMenuFromCookie = () => {
  menuListStore.restoreMenuListInfo();
  menuList.value = menuListStore.getMenuList;
};

onMounted(() => {
  loadMenuFromCookie();
});
</script>

<style lang="scss" scoped>
.menu-box {
  height: 100%;
  display: flex;
  flex-direction: column;

  .logo-container {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 18px;
    background-color: #f2f9ff;
    border-bottom: 1px solid #f2f9ff;

    .logo-title {
      color: #000;
      font-size: 18px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-menu-vertical-demo {
    border-right: none;
    flex: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }
  }

  :deep(.el-sub-menu__title):hover {
    background-color: #e6f4ff !important;
  }

  :deep(.el-menu-item):hover {
    background-color: #e6f4ff !important;
  }

  :deep(.el-menu-item.is-active) {
    background-color: #e6f4ff !important;
    color: #409eff !important;
  }
}
</style>