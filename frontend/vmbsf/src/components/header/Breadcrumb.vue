<template>
  <div class="bread-box">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/index' }">
        <el-icon>
          <HomeFilled />
        </el-icon>
        首页
      </el-breadcrumb-item>
      <el-breadcrumb-item v-for="(item, index) in urlArr" :key="index">
                <span v-if="index === urlArr.length - 1 && item.meta.parentName" class="current-page">
                    <b>{{ item.meta.parentName }}&nbsp;&nbsp;/&nbsp;&nbsp;{{ item.meta.title }}</b>
                </span>
        <span v-else>
                    <b> {{ item.meta.title }}</b>
                </span>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { HomeFilled } from "@element-plus/icons-vue";
import { ref, watch } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const urlArr = ref([]);

const initUrlArr = () => {
  // 过滤掉首页路由，避免重复显示
  urlArr.value = route.matched.filter(item => item.meta.title && item.path !== '/index');
  console.log("urlArr:", urlArr.value);
};

watch(
    route,
    () => {
      initUrlArr();
    },
    { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.bread-box {
  padding: 25px 20px;

  b {
    font-size: 14px;
    color: #4176df;
    font-weight: bold;

    &:hover {
      color: #409eff;
    }
  }



  :deep(.el-breadcrumb) {
    font-size: 14px;

    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        color: #4176df;
        font-weight: bold;

        &:hover {
          color: #409eff;
        }
      }

      &:last-child {
        .el-breadcrumb__inner {
          color: #303133;
          font-weight: 600;
        }
      }

      .el-breadcrumb__separator {
        color: #4176df;
        margin: 0 5px;
        font-weight: bold;
      }
    }
  }

  .current-page {
    color: #303133;
    font-weight: 600;
  }

  .el-icon {
    margin-right: 6px;
    vertical-align: middle;
  }
}
</style>
