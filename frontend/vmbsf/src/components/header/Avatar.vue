<template>
  <el-dropdown>
        <span class="el-dropdown-link">
            <el-avatar shape="square" :size="40" :src="userStore.getAvatar" />
            <el-badge> {{ userStore.getUsername }}</el-badge>
            <el-icon class="el-icon--right">
                <arrow-down />
            </el-icon>
        </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item>
          <router-link to="{path: '/index/userCenter'}">
          个人中心</router-link>
        </el-dropdown-item>
        <el-dropdown-item @click="logoutBtn">安全退出</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { ArrowDown } from "@element-plus/icons-vue";
import { useUserStore } from "../../stores/user";
import { useMenuListStore } from "../../stores/menuList";
import { logout } from "../../api/user";
import { useTokenStore } from "../../stores/token";
import { useRouter } from "vue-router";
import {ElMessage} from "element-plus";
import { useHasRoutesStore } from "../../stores/hasRoutes";
import store from "../../stores/index";
//1.从stores中获取当前登录的用户信息
const userStore = useUserStore();
const menuListStore = useMenuListStore();
const tokenStore = useTokenStore();
const router = useRouter();
const hasRoutesStore = useHasRoutesStore();

const logoutBtn = async () => {
  let { code } = await logout();
  if (code === 200) {
    //清空cookie和localStorage中的值
    userStore.logout();
    menuListStore.removeMenuList();
    tokenStore.removeToken();
    hasRoutesStore.resetHasRoutes();
    //清空store中的值
    store.commit("RESET_TABS");
    //跳转页面
    router.push({ name: "login" });
  } else {
    ElMessage.error("退出失败！");
  }
};
</script>

<style lang="scss" scoped></style>
