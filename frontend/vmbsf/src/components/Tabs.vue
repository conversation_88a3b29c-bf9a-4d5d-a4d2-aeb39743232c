<template>
  <div class="tabs">
    <el-tabs v-model="editableTabsValue" type="card" class="demo-tabs" closable @tab-remove="removeTab"
             @tab-click="clickTab">
      <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
        {{ item.content }}
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import store from "../stores/index";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ta } from "element-plus/es/locales.mjs";
//选中选项卡
const editableTabsValue = ref("/welcome");
//选项卡数组
const editableTabs = ref([]);
//创建路由
const router = useRouter();

//vue3的生命周期函数
onMounted(() => {
  //从store中获取值
  editableTabsValue.value = store.getters.GET_DEFAULT_TAB_VALUE;
  editableTabs.value = store.getters.GET_TABS;
  //跳转到指定页面
  router.push({ path: editableTabsValue.value });
});

const removeTab = (targetName) => {
  // 永远不允许关闭欢迎页面
  if (targetName === "/index/welcome") {
    ElMessage({
      showClose: true,
      message: "欢迎页面不允许关闭!",
    });
    return;
  }

  const tabs = editableTabs.value;
  console.log('tabs', tabs)
  let activeName = editableTabsValue.value;

  // 如果关闭的是当前激活的tab，则需要选择下一个tab
  if (activeName === targetName) {
    tabs.forEach((tab, index) => {
      if (tab.name === targetName) {
        const nextTab = tabs[index + 1] || tabs[index - 1];
        if (nextTab) {
          activeName = nextTab.name;
        }
      }
    });
  }

  editableTabsValue.value = activeName;
  editableTabs.value = tabs.filter((tab) => tab.name !== targetName);
  //更新store中的数据
  store.commit("SET_TABS", editableTabs.value);
  store.commit("SET_DEFAULT_TAB_VALUE", editableTabsValue.value);
  //路由跳转到下一个Tab
  router.push({ path: activeName });
};

//切换选项卡是执行
const clickTab = (targetName) => {
  console.log("正在切换选项卡", targetName.props);
  //name 路由路由  label 路由名称
  editableTabsValue.value = targetName.props.name;
  store.commit("SET_DEFAULT_TAB_VALUE", editableTabsValue.value);
  // //路由跳转
  router.push({ path: targetName.props.name });
};

// 监听store中 state.defaultTabValue 的变化
watch(
    () => store.state.defaultTabValue,
    (newValue) => {
      editableTabsValue.value = newValue;
    }
);
</script>

<style lang="scss" scoped>
.demo-tabs>.el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
