import axios from "axios";
//加载组件
import { ElLoading } from "element-plus";
import cookie from 'js-cookie'

//1. 创建新的axios实例，
var loadingInstance = null;
const service = axios.create({
    // 公共接口 开发环境还是线上环境也可以用api
    baseURL: "http://localhost:8080",
    // 超时时间 单位是ms，这里设置了5s的超时时间
    timeout: 50000,
});
// 2.请求拦截器
service.interceptors.request.use(
    (config) => {
        //注意使用token的时候需要引入cookie方法或者用本地localStorage等方法，推荐js-cookie
        const token =cookie.get('token');
        // 判断是否存在token，如果存在的话，则每个http header都加上token
        if(token){
            config.params = {'token':token} //如果要求携带在参数中
            config.headers.token= token; //如果要求携带在请求头中
        }
        loadingInstance = ElLoading.service(); //加载中....
        return config;
    },
    (error) => {
        Promise.reject(error);
    }
);

// 3.响应拦截器
service.interceptors.response.use(
    (response) => {
        loadingInstance.close(); //关闭loading
        if (response.data.code !== 200) {
            return response.data; // 就是后端返回的result对象
        } else {
            return response.data; // 就是后端返回的result对象
        }
    },
    (error) => {
        /***** 接收到异常响应的处理开始 *****/
        loadingInstance.close(); //关闭loading
        // 返回统一格式的错误对象
        return Promise.resolve({
            code: 500,
            message: "服务器错误!",
            data: null
        });
    }
);
//4.导出
export default service;