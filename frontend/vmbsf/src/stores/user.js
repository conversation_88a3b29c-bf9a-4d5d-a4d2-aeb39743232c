import { defineStore } from "pinia";
import cookie from "js-cookie";

export const useUserStore = defineStore("user", {
    state: () => ({
        userInfo: {
            id: null,
            phone_number: null,
            username: null,
            password_hash: null,
            real_name: null,
            gender: null,
            avatar_url: null,
            login_date: null,
            status: null,
            store_id: null,
            updated_at: null,
            created_at: null,
            is_delete: null,
        },
        //登录状态
        isLoggedIn: false,
    }),
    //getters 相当于 Vue 组件中的 computed 计算属性，用于从 state 中派生出一些状态。
    // 从 state 中计算派生值, 缓存结果，只有依赖的数据变化时才会重新计算
    getters: {
        // 获取用户信息
        getUserInfo: (state) => state.userInfo,
        // 获取登录状态
        getIsLoggedIn: (state) => state.isLoggedIn,
        // 获取用户ID
        getId: (state) => state.userInfo.id,
        // 获取用户名
        getUsername: (state) => state.userInfo.username,
        // 获取头像
        getAvatar: (state) => state.userInfo.avatar_url,
    },
    // actions 相当于 Vue 组件中的 methods，用于处理业务逻辑和修改 state。
    actions: {
        // 用户登录
        updateUser(userData) {
            //将参数赋值 userInfo
            this.userInfo = { ...userData };
            // 设置登录状态为 true
            this.isLoggedIn = true;
            // 可选：保存到 cookie
            cookie.set("userInfo", JSON.stringify(this.userInfo), {
                path: "/",
                expires: 7,
            });
        },

        // 用户登出
        logout() {
            // 重置状态
            this.userInfo = {
                id: null,
                phone_number: null,
                username: null,
                password_hash: null,
                real_name: null,
                gender: null,
                avatar_url: null,
                login_date: null,
                status: null,
                store_id: null,
                updated_at: null,
                created_at: null,
                is_delete: null,
            };
            // 重置登录状态为 false
            this.isLoggedIn = false;
            // 清除本地存储
            cookie.remove("userInfo");
        },

        //更新用户头像
        updateUserAvatar(newAvatar) {
            this.userInfo.avatar_url = newAvatar;
            cookie.set("userInfo", JSON.stringify(this.userInfo), {
                path: "/",
                expires: 7,
            });
        },

        // 更新用户信息
        updateUserInfo(newInfo) {
            this.userInfo = { ...this.userInfo, ...newInfo };
            // 更新本地存储
            cookie.set("userInfo", JSON.stringify(this.userInfo), {
                path: "/",
                expires: 7,
            });
        },

        // 从本地存储恢复用户信息
        restoreUserInfo() {
            // 从 cookie 中获取用户信息
            const savedUser = cookie.get("userInfo");
            if (savedUser) {
                this.userInfo = JSON.parse(savedUser);
                this.isLoggedIn = true;
            }
        },
    },
});
