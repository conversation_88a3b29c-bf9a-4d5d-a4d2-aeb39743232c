import { defineStore } from "pinia";

/**
 * 路由状态管理 store
 * 用于管理动态路由是否已绑定的状态，避免重复绑定路由
 */
export const useHasRoutesStore = defineStore("hasRoutes", {
    // state 状态 - 存储应用数据的地方
    state: () => ({
        hasRoutes: false, // 用于判断是否已经绑定过动态路由
    }),

    // getters - 相当于 Vue 组件中的 computed，用于计算和获取状态值
    getters: {
        /**
         * 获取路由绑定状态
         * @param {Object} state - 当前状态对象
         * @returns {boolean} 是否已绑定动态路由
         */
        getHasRoutes: (state) => state.hasRoutes,
    },

    // actions - 相当于 Vue 组件中的 methods，用于处理业务逻辑和修改 state
    actions: {
        /**
         * 更新路由绑定状态
         * @param {boolean} val - 路由绑定状态值
         */
        updateHasRoutes(val) {
            this.hasRoutes = Boolean(val);
            // 将状态持久化存储到 localStorage
            localStorage.setItem("hasRoutes", Bo<PERSON>an(val).toString());
        },
        /**
         * 从本地存储恢复路由绑定状态
         * 页面刷新后用于恢复路由状态
         */
        restoreHasRoutes() {
            try {
                const hasRoutesStr = localStorage.getItem("hasRoutes");
                this.hasRoutes = hasRoutesStr === "true";
            } catch (error) {
                console.error("Failed to restore hasRoutes from localStorage:", error);
                this.hasRoutes = false;
            }
        },

        /**
         * 清除路由状态并重新初始化
         */
        resetHasRoutes() {
            this.hasRoutes = false;
            // 从 localStorage 中移除状态
            localStorage.removeItem("hasRoutes");
        },
    },
});
