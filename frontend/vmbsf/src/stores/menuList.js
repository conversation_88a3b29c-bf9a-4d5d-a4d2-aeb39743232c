import { defineStore } from "pinia";

/**
 * 菜单列表状态管理 store
 * 使用 localStorage 存储菜单数据，避免 cookie 大小限制
 */
export const useMenuListStore = defineStore("menuList", {
    state: () => ({
        menuList: [], // 用户菜单列表
    }),

    getters: {
        /**
         * 获取菜单列表
         * @param {Object} state - 当前状态对象
         * @returns {Array} 菜单列表数据
         */
        getMenuList: (state) => state.menuList,
    },

    actions: {
        /**
         * 更新菜单列表
         * @param {Array} val - 新的菜单列表数据
         */
        updateMenuList(val) {
            // 深拷贝避免引用问题
            this.menuList = JSON.parse(JSON.stringify(val));

            try {
                // 使用 localStorage 存储，容量通常为 5-10MB
                localStorage.setItem("menuList", JSON.stringify(val));
                console.log("保存菜单列表到 localStorage");
            } catch (error) {
                console.error("menuList保存到localStorage失败:", error);
            }
        },

        /**
         * 清除菜单列表
         */
        removeMenuList() {
            this.menuList = [];
            try {
                localStorage.removeItem("menuList");
            } catch (error) {
                console.error("Failed to remove menuList from localStorage:", error);
            }
        },

        /**
         * 从本地存储恢复菜单信息
         * 页面刷新后用于恢复用户菜单权限
         */
        restoreMenuListInfo() {
            try {
                // 从 localStorage 中获取菜单信息
                const menuListStr = localStorage.getItem("menuList");
                if (menuListStr) {
                    this.menuList = JSON.parse(menuListStr);
                }
            } catch (error) {
                console.error("Failed to parse menuList from localStorage:", error);
                this.menuList = [];
            }
        },
    },
});
