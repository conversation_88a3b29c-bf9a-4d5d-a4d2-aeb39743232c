// 使用 Vuex 做状态管理
import { createStore } from "vuex";

// 创建 Vuex store 实例
const store = createStore({
    // state - 存储应用的状态数据
    state: {
        // 管理的数据

        // 默认选中的 tab 页路径
        defaultTabValue: "/index/welcome",

        // 存储选项卡的数组，用于在页面顶部显示可切换的标签页
        // 每个 tab 对象包含 title(标签页标题) 和 name(路由路径)
        tabs: [{ title: "欢迎页面", name: "/index/welcome" }],
    },

    // mutations - 同步修改 state 中数据的方法
    mutations: {
        /**
         * 添加新的选项卡
         * @param {Object} state - 当前状态对象
         * @param {Object} tab - 要添加的选项卡对象，包含 name(路由名称) 和 path(路由路径)
         */
        ADD_TABS: (state, tab) => {
            console.log("tab--->", tab.name, tab.path);

            // 判断数组中是否已经存在此选项卡（根据路由路径判断）
            // findIndex 返回 -1 表示未找到，即该选项卡不存在
            if (state.tabs.findIndex((e) => e.name === tab.path) === -1) {
                // 装入到数组中
                // title 路由名称  name 路由路径
                state.tabs.push({ title: tab.name, name: tab.path });

                // 设置当前选中的选项卡为新添加的选项卡
                state.defaultTabValue = tab.path;
            }
        },

        /**
         * 重置选项卡状态（通常在用户退出登录时调用）
         * @param {Object} state - 当前状态对象
         */
        RESET_TABS: (state) => {
            // 退出登录的时候,重置选项卡
            state.defaultTabValue = "/index/welcome";
            state.tabs = [{ title: "欢迎页面", name: "/index/welcome" }];
        },

        /**
         * 设置选项卡数组
         * @param {Object} state - 当前状态对象
         * @param {Array} tabs - 新的选项卡数组
         */
        SET_TABS: (state, tabs) => {
            state.tabs = tabs;
        },

        /**
         * 设置默认选中的选项卡
         * @param {Object} state - 当前状态对象
         * @param {string} defaultTabValue - 要设置为默认选中的选项卡路径
         */
        SET_DEFAULT_TAB_VALUE(state, defaultTabValue) {
            state.defaultTabValue = defaultTabValue;
        },
    },

    // actions - 异步操作处理（当前为空，可根据需要添加异步操作）
    actions: {},

    // getters - 计算属性，用于从 state 中派生出一些状态
    getters: {
        /**
         * 获取默认选中的选项卡路径
         * @param {Object} state - 当前状态对象
         * @returns {string} 默认选中的选项卡路径
         */
        GET_DEFAULT_TAB_VALUE: (state) => {
            return state.defaultTabValue;
        },

        /**
         * 获取所有选项卡数组
         * @param {Object} state - 当前状态对象
         * @returns {Array} 选项卡数组
         */
        GET_TABS: (state) => {
            return state.tabs;
        },
    },

    // modules - 模块化 Vuex（当前为空，可根据需要拆分模块）
    modules: {},
});

// 对外暴露 store 实例
export default store;
