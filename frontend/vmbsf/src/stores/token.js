import { defineStore } from "pinia";
import cookie from "js-cookie";

export const useTokenStore = defineStore("token", {
    // state 状态  是存储应用数据的地方，相当于 Vue 组件中的 data。
    state: () => ({
        token: null,
    }),
    getters: {
        // 获取token信息
        getToken: (state) => state.token,
    },
    // actions 相当于 Vue 组件中的 methods，用于处理业务逻辑和修改 state。
    actions: {
        //方法1:修改token值
        updateToken(val) {
            this.token = val;
            // 将token保存在浏览器的cookie中
            cookie.set("token", val, { path: '/', expires: 7 });
        },
        // 方法2:删除token
        removeToken() {
            this.token=null;
            // 将token从浏览器的cookie中删除
            cookie.remove("token");
        },
    },
});
