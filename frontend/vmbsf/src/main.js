import { createApp } from "vue";

import App from "./App.vue"; //导入根组件
import router from "./router"; //导入路由
import ElementPlus from "element-plus"; //导入element ui
import "element-plus/dist/index.css"; //导入 element ui 样式
import { createPinia } from "pinia"; //导入pinia 全局状态管理器
import piniaPluginPersist from "pinia-plugin-persistedstate"; //导入pinia持久化插件

const app = createApp(App); //创建Vue实例
const pinia = createPinia(); //创建pinia实例
pinia.use(piniaPluginPersist); //挂载pinia插件
app.use(ElementPlus); //挂载elementPlus
//引入pinia (必须在 router 之前使用 pinia)
app.use(pinia) //挂载pinia
app.use(router); //挂载路由
app.mount("#app");