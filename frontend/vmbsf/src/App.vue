<template>
  <!-- 国际化处理 -->
  <el-config-provider :locale="zhCn">
    <!-- 路由视图 -->
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup>
import { zhCn } from "element-plus/es/locales.mjs";
//  watch 监听
import { watch } from "vue";
//  路由
import { useRoute } from "vue-router";
//  存储
import store from "./stores/index";
// 创建路由变量
const route = useRoute();
// 不用创建tab的路径 (白名单)
const whitePath = ["/login", "/index", "/"];
// 监听 route路由的变化
watch(
    route,
    (to, from) => {
      console.info("App.vue监听到路由发生改变,是否创建选项卡:", whitePath.indexOf(to.path) === -1);
      if (whitePath.indexOf(to.path) === -1) {
        // 不是白名单中的路径，则需要创建新的Tab 选项卡，显示这个界面
        store.commit("ADD_TABS", { name: to.meta.title, path: to.path });
        store.commit("SET_DEFAULT_TAB_VALUE", to.path);
      }
    },
    { deep: true, immediate: true }
); // deep:深度监听,immediate:立即监听
</script>

<style scoped></style>
