import request from '../utils/axios.js'
//步骤1：导入qs模块
import qs  from 'qs'

//用户登录
export const login = (data) => {
    //步骤2：调用qs的方法将data进行序列化 (传递的表单，因为请求是SpringSecurity提供的默认form)
    let str = qs.stringify(data)  //username=user&password_hash=736f7b66-e1be-44a1-963f-ecbae92e403c
    return request({
        url: `/login?`+str,
        method: 'post'
    })
}

//获取用户信息
export const getUserInfo = (id) => {
    return request({
        url: `/users/${id}`,
        method: 'get'
    })
}

//用户退出
export const logout = () => {
    return request({
        url: `/logout`,
        method: 'get'
    })
}

//分页获取用户信息列表的方法
export const getUserList = (data) => {
    return request({
        url: `/users/list`,
        method: "post",
        data: data,
    });
};