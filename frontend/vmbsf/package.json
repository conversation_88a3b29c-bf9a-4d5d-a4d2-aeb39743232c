{"name": "vmbsf", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.12.2", "element-plus": "^2.11.3", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "qs": "^6.14.0", "sass": "^1.93.0", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.1.0", "vue": "^3.5.21", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "^7.1.7"}}